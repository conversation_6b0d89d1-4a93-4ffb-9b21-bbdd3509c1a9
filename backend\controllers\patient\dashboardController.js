const HealthMetrics = require('../../models/patient/HealthMetrics');
const HealthReading = require('../../models/patient/HealthReading');
const mongoose = require('mongoose');

// Get complete dashboard data for patient
const getDashboardData = async (req, res) => {
  try {
    const patientId = req.user._id;
    
    // Get current health metrics
    let healthMetrics = await HealthMetrics.findByPatientId(patientId);
    
    if (!healthMetrics) {
      // Create default health metrics if none exist
      healthMetrics = new HealthMetrics({ patientId });
      await healthMetrics.save();
    }
    
    // Get chart data for all metrics (last 7 days)
    const chartDataPromises = [
      HealthReading.getWeeklyChartData(patientId, 'heartRate'),
      HealthReading.getWeeklyChartData(patientId, 'bloodPressure'),
      HealthReading.getWeeklyChartData(patientId, 'weight'),
      HealthReading.getWeeklyChartData(patientId, 'bloodSugar')
    ];
    
    const [heartRateChart, bloodPressureChart, weightChart, bloodSugarChart] = await Promise.all(chartDataPromises);
    
    // Get recent readings (last 10 for each metric)
    const recentReadingsPromises = [
      HealthReading.getReadingsByPatient(patientId, { metricType: 'heartRate', limit: 10 }),
      HealthReading.getReadingsByPatient(patientId, { metricType: 'bloodPressure', limit: 10 }),
      HealthReading.getReadingsByPatient(patientId, { metricType: 'weight', limit: 10 }),
      HealthReading.getReadingsByPatient(patientId, { metricType: 'bloodSugar', limit: 10 })
    ];
    
    const [heartRateReadings, bloodPressureReadings, weightReadings, bloodSugarReadings] = await Promise.all(recentReadingsPromises);
    
    // Calculate trends (comparing last reading with previous)
    const calculateTrend = (readings) => {
      if (readings.length < 2) return 'stable';
      const latest = readings[0].value;
      const previous = readings[1].value;
      const change = ((latest - previous) / previous) * 100;
      
      if (change > 5) return 'increasing';
      if (change < -5) return 'decreasing';
      return 'stable';
    };
    
    // Get health alerts (readings with alerts in last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const alertReadings = await HealthReading.find({
      patientId,
      isAlert: true,
      readingTime: { $gte: thirtyDaysAgo }
    }).sort({ readingTime: -1 }).limit(10);
    
    // Prepare response data
    const dashboardData = {
      healthMetrics: healthMetrics.getFormattedMetrics(),
      chartData: {
        heartRate: heartRateChart,
        bloodPressure: bloodPressureChart,
        weight: weightChart,
        bloodSugar: bloodSugarChart
      },
      trends: {
        heartRate: calculateTrend(heartRateReadings),
        bloodPressure: calculateTrend(bloodPressureReadings),
        weight: calculateTrend(weightReadings),
        bloodSugar: calculateTrend(bloodSugarReadings)
      },
      recentReadings: {
        heartRate: heartRateReadings.slice(0, 5),
        bloodPressure: bloodPressureReadings.slice(0, 5),
        weight: weightReadings.slice(0, 5),
        bloodSugar: bloodSugarReadings.slice(0, 5)
      },
      alerts: alertReadings,
      summary: {
        totalReadings: heartRateReadings.length + bloodPressureReadings.length + weightReadings.length + bloodSugarReadings.length,
        alertCount: alertReadings.length,
        lastUpdated: healthMetrics.updatedAt
      }
    };
    
    res.status(200).json({
      success: true,
      data: dashboardData
    });
    
  } catch (error) {
    console.error('Error fetching dashboard data:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching dashboard data',
      error: error.message
    });
  }
};

// Get health overview data (simplified version for quick loading)
const getHealthOverview = async (req, res) => {
  try {
    const patientId = req.user._id;
    
    // Get current health metrics
    let healthMetrics = await HealthMetrics.findByPatientId(patientId);
    
    if (!healthMetrics) {
      // Create default health metrics if none exist
      healthMetrics = new HealthMetrics({ patientId });
      await healthMetrics.save();
    }
    
    // Get latest readings for each metric
    const latestReadingsPromises = [
      HealthReading.getLatestReading(patientId, 'heartRate'),
      HealthReading.getLatestReading(patientId, 'bloodPressure'),
      HealthReading.getLatestReading(patientId, 'weight'),
      HealthReading.getLatestReading(patientId, 'bloodSugar')
    ];
    
    const [latestHeartRate, latestBloodPressure, latestWeight, latestBloodSugar] = await Promise.all(latestReadingsPromises);
    
    // Get basic chart data (last 7 days for heart rate as default)
    const chartData = await HealthReading.getWeeklyChartData(patientId, 'heartRate');
    
    const overviewData = {
      healthMetrics: healthMetrics.getFormattedMetrics(),
      latestReadings: {
        heartRate: latestHeartRate,
        bloodPressure: latestBloodPressure,
        weight: latestWeight,
        bloodSugar: latestBloodSugar
      },
      chartData: chartData,
      lastUpdated: healthMetrics.updatedAt
    };
    
    res.status(200).json({
      success: true,
      data: overviewData
    });
    
  } catch (error) {
    console.error('Error fetching health overview:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching health overview',
      error: error.message
    });
  }
};

// Get health statistics
const getHealthStats = async (req, res) => {
  try {
    const patientId = req.user._id;
    const { period = 30 } = req.query; // Default to last 30 days
    
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(period));
    
    // Get readings count for each metric in the period
    const statsPromises = [
      HealthReading.countDocuments({ patientId, metricType: 'heartRate', readingTime: { $gte: startDate } }),
      HealthReading.countDocuments({ patientId, metricType: 'bloodPressure', readingTime: { $gte: startDate } }),
      HealthReading.countDocuments({ patientId, metricType: 'weight', readingTime: { $gte: startDate } }),
      HealthReading.countDocuments({ patientId, metricType: 'bloodSugar', readingTime: { $gte: startDate } }),
      HealthReading.countDocuments({ patientId, isAlert: true, readingTime: { $gte: startDate } })
    ];
    
    const [heartRateCount, bloodPressureCount, weightCount, bloodSugarCount, alertCount] = await Promise.all(statsPromises);
    
    // Get average values for the period
    const avgPromises = [
      HealthReading.aggregate([
        { $match: { patientId: new mongoose.Types.ObjectId(patientId), metricType: 'heartRate', readingTime: { $gte: startDate } } },
        { $group: { _id: null, avg: { $avg: '$value' } } }
      ]),
      HealthReading.aggregate([
        { $match: { patientId: new mongoose.Types.ObjectId(patientId), metricType: 'weight', readingTime: { $gte: startDate } } },
        { $group: { _id: null, avg: { $avg: '$value' } } }
      ]),
      HealthReading.aggregate([
        { $match: { patientId: new mongoose.Types.ObjectId(patientId), metricType: 'bloodSugar', readingTime: { $gte: startDate } } },
        { $group: { _id: null, avg: { $avg: '$value' } } }
      ])
    ];
    
    const [avgHeartRate, avgWeight, avgBloodSugar] = await Promise.all(avgPromises);
    
    const stats = {
      period: parseInt(period),
      readingCounts: {
        heartRate: heartRateCount,
        bloodPressure: bloodPressureCount,
        weight: weightCount,
        bloodSugar: bloodSugarCount,
        total: heartRateCount + bloodPressureCount + weightCount + bloodSugarCount
      },
      averages: {
        heartRate: avgHeartRate[0]?.avg ? Math.round(avgHeartRate[0].avg) : null,
        weight: avgWeight[0]?.avg ? Math.round(avgWeight[0].avg * 10) / 10 : null,
        bloodSugar: avgBloodSugar[0]?.avg ? Math.round(avgBloodSugar[0].avg) : null
      },
      alertCount,
      healthScore: calculateHealthScore(alertCount, heartRateCount + bloodPressureCount + weightCount + bloodSugarCount)
    };
    
    res.status(200).json({
      success: true,
      data: stats
    });
    
  } catch (error) {
    console.error('Error fetching health stats:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching health stats',
      error: error.message
    });
  }
};

// Helper function to calculate health score
const calculateHealthScore = (alertCount, totalReadings) => {
  if (totalReadings === 0) return 85; // Default score
  
  const alertRatio = alertCount / totalReadings;
  let score = 100;
  
  if (alertRatio > 0.3) score -= 30; // High alert ratio
  else if (alertRatio > 0.2) score -= 20;
  else if (alertRatio > 0.1) score -= 10;
  else if (alertRatio > 0.05) score -= 5;
  
  // Bonus for regular monitoring
  if (totalReadings > 20) score += 5;
  else if (totalReadings > 10) score += 3;
  
  return Math.max(60, Math.min(100, score)); // Keep score between 60-100
};

module.exports = {
  getDashboardData,
  getHealthOverview,
  getHealthStats
};
