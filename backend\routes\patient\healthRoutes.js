const express = require('express');
const router = express.Router();
const { protect, requirePatientRole } = require('../../middleware/authMiddleware');
const {
  getHealthMetrics,
  updateHealthMetrics,
  getHealthReadings,
  getChartData,
  getLatestReading
} = require('../../controllers/patient/healthController');

// Apply authentication middleware to all routes
router.use(protect);
router.use(requirePatientRole);

// Health metrics routes
router.get('/metrics', getHealthMetrics);
router.put('/metrics', updateHealthMetrics);

// Health readings routes
router.get('/readings', getHealthReadings);
router.get('/readings/latest/:metricType', getLatestReading);

// Chart data routes
router.get('/chart-data', getChartData);

module.exports = router;
