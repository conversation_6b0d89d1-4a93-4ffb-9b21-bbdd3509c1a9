const HealthMetrics = require('../../models/patient/HealthMetrics');
const HealthReading = require('../../models/patient/HealthReading');
const mongoose = require('mongoose');

// Get patient's current health metrics
const getHealthMetrics = async (req, res) => {
  try {
    const patientId = req.user._id;
    
    let healthMetrics = await HealthMetrics.findByPatientId(patientId);
    
    if (!healthMetrics) {
      // Create default health metrics if none exist
      healthMetrics = new HealthMetrics({ patientId });
      await healthMetrics.save();
    }
    
    res.status(200).json({
      success: true,
      data: healthMetrics.getFormattedMetrics()
    });
  } catch (error) {
    console.error('Error fetching health metrics:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching health metrics',
      error: error.message
    });
  }
};

// Update patient's health metrics
const updateHealthMetrics = async (req, res) => {
  try {
    const patientId = req.user._id;
    const { heartRate, bloodPressure, weight, bloodSugar, height } = req.body;
    
    // Validate input data
    const updates = {};
    
    if (heartRate !== undefined) {
      if (heartRate < 30 || heartRate > 220) {
        return res.status(400).json({
          success: false,
          message: 'Heart rate must be between 30 and 220 bpm'
        });
      }
      updates.heartRate = {
        current: heartRate,
        unit: 'bpm',
        lastUpdated: new Date()
      };
    }
    
    if (bloodPressure !== undefined) {
      const { systolic, diastolic } = bloodPressure;
      if (!systolic || !diastolic || systolic < 70 || systolic > 250 || diastolic < 40 || diastolic > 150) {
        return res.status(400).json({
          success: false,
          message: 'Invalid blood pressure values'
        });
      }
      updates.bloodPressure = {
        systolic,
        diastolic,
        unit: 'mmHg',
        lastUpdated: new Date()
      };
    }
    
    if (weight !== undefined) {
      if (weight < 20 || weight > 500) {
        return res.status(400).json({
          success: false,
          message: 'Weight must be between 20 and 500 kg'
        });
      }
      updates.weight = {
        current: weight,
        unit: 'kg',
        lastUpdated: new Date()
      };
    }
    
    if (bloodSugar !== undefined) {
      const { value, testType = 'random' } = bloodSugar;
      if (value < 50 || value > 600) {
        return res.status(400).json({
          success: false,
          message: 'Blood sugar must be between 50 and 600 mg/dL'
        });
      }
      updates.bloodSugar = {
        current: value,
        testType,
        unit: 'mg/dL',
        lastUpdated: new Date()
      };
    }
    
    if (height !== undefined) {
      if (height < 50 || height > 250) {
        return res.status(400).json({
          success: false,
          message: 'Height must be between 50 and 250 cm'
        });
      }
      updates.height = {
        current: height,
        unit: 'cm',
        lastUpdated: new Date()
      };
    }
    
    // Update or create health metrics
    const healthMetrics = await HealthMetrics.createOrUpdateMetrics(patientId, updates);
    
    // Create health readings for historical tracking
    const readingPromises = [];
    
    if (heartRate !== undefined) {
      readingPromises.push(
        new HealthReading({
          patientId,
          metricType: 'heartRate',
          value: heartRate,
          unit: 'bpm',
          source: 'manual'
        }).save()
      );
    }
    
    if (bloodPressure !== undefined) {
      readingPromises.push(
        new HealthReading({
          patientId,
          metricType: 'bloodPressure',
          value: `${bloodPressure.systolic}/${bloodPressure.diastolic}`,
          unit: 'mmHg',
          additionalData: {
            systolic: bloodPressure.systolic,
            diastolic: bloodPressure.diastolic
          },
          source: 'manual'
        }).save()
      );
    }
    
    if (weight !== undefined) {
      readingPromises.push(
        new HealthReading({
          patientId,
          metricType: 'weight',
          value: weight,
          unit: 'kg',
          source: 'manual'
        }).save()
      );
    }
    
    if (bloodSugar !== undefined) {
      readingPromises.push(
        new HealthReading({
          patientId,
          metricType: 'bloodSugar',
          value: bloodSugar.value,
          unit: 'mg/dL',
          additionalData: {
            testType: bloodSugar.testType || 'random'
          },
          source: 'manual'
        }).save()
      );
    }
    
    if (height !== undefined) {
      readingPromises.push(
        new HealthReading({
          patientId,
          metricType: 'height',
          value: height,
          unit: 'cm',
          source: 'manual'
        }).save()
      );
    }
    
    // Save all readings
    await Promise.all(readingPromises);
    
    // Emit real-time update if socket.io is available
    if (req.app.get('io')) {
      req.app.get('io').to(`patient_${patientId}`).emit('healthMetricsUpdated', {
        patientId,
        metrics: healthMetrics.getFormattedMetrics()
      });
    }
    
    res.status(200).json({
      success: true,
      message: 'Health metrics updated successfully',
      data: healthMetrics.getFormattedMetrics()
    });
    
  } catch (error) {
    console.error('Error updating health metrics:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating health metrics',
      error: error.message
    });
  }
};

// Get health readings history
const getHealthReadings = async (req, res) => {
  try {
    const patientId = req.user._id;
    const { metricType, startDate, endDate, limit = 50 } = req.query;
    
    const options = {
      metricType,
      startDate,
      endDate,
      limit: parseInt(limit)
    };
    
    const readings = await HealthReading.getReadingsByPatient(patientId, options);
    
    res.status(200).json({
      success: true,
      data: readings
    });
  } catch (error) {
    console.error('Error fetching health readings:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching health readings',
      error: error.message
    });
  }
};

// Get chart data for dashboard
const getChartData = async (req, res) => {
  try {
    const patientId = req.user._id;
    const { metricType = 'heartRate', days = 7 } = req.query;
    
    const chartData = await HealthReading.getWeeklyChartData(patientId, metricType);
    
    res.status(200).json({
      success: true,
      data: chartData
    });
  } catch (error) {
    console.error('Error fetching chart data:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching chart data',
      error: error.message
    });
  }
};

// Get latest reading for a specific metric
const getLatestReading = async (req, res) => {
  try {
    const patientId = req.user._id;
    const { metricType } = req.params;
    
    const reading = await HealthReading.getLatestReading(patientId, metricType);
    
    res.status(200).json({
      success: true,
      data: reading
    });
  } catch (error) {
    console.error('Error fetching latest reading:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching latest reading',
      error: error.message
    });
  }
};

module.exports = {
  getHealthMetrics,
  updateHealthMetrics,
  getHealthReadings,
  getChartData,
  getLatestReading
};
