const mongoose = require('mongoose');

const healthMetricsSchema = new mongoose.Schema({
  patientId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true, // Each patient has only one health metrics record
    index: true
  },
  heartRate: {
    current: {
      type: Number,
      min: 30,
      max: 220
    },
    unit: {
      type: String,
      default: 'bpm'
    },
    lastUpdated: {
      type: Date,
      default: Date.now
    }
  },
  bloodPressure: {
    systolic: {
      type: Number,
      min: 70,
      max: 250
    },
    diastolic: {
      type: Number,
      min: 40,
      max: 150
    },
    unit: {
      type: String,
      default: 'mmHg'
    },
    lastUpdated: {
      type: Date,
      default: Date.now
    }
  },
  weight: {
    current: {
      type: Number,
      min: 20,
      max: 500
    },
    unit: {
      type: String,
      default: 'kg'
    },
    lastUpdated: {
      type: Date,
      default: Date.now
    }
  },
  bloodSugar: {
    current: {
      type: Number,
      min: 50,
      max: 600
    },
    unit: {
      type: String,
      default: 'mg/dL'
    },
    testType: {
      type: String,
      enum: ['fasting', 'random', 'postprandial', 'hba1c'],
      default: 'random'
    },
    lastUpdated: {
      type: Date,
      default: Date.now
    }
  },
  height: {
    current: {
      type: Number,
      min: 50,
      max: 250
    },
    unit: {
      type: String,
      default: 'cm'
    },
    lastUpdated: {
      type: Date,
      default: Date.now
    }
  },
  // Calculated fields
  bmi: {
    type: Number,
    min: 10,
    max: 100
  },
  // Overall health status
  healthStatus: {
    type: String,
    enum: ['excellent', 'good', 'fair', 'poor', 'critical'],
    default: 'good'
  },
  // Timestamps
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Pre-save middleware to calculate BMI and update timestamps
healthMetricsSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  
  // Calculate BMI if height and weight are available
  if (this.height?.current && this.weight?.current) {
    const heightInMeters = this.height.current / 100;
    this.bmi = Number((this.weight.current / (heightInMeters * heightInMeters)).toFixed(1));
  }
  
  next();
});

// Instance methods
healthMetricsSchema.methods.updateMetric = function(metricType, value, additionalData = {}) {
  if (this[metricType]) {
    this[metricType].current = value;
    this[metricType].lastUpdated = new Date();
    
    // Handle additional data for specific metrics
    if (metricType === 'bloodPressure' && additionalData.systolic && additionalData.diastolic) {
      this[metricType].systolic = additionalData.systolic;
      this[metricType].diastolic = additionalData.diastolic;
    }
    
    if (metricType === 'bloodSugar' && additionalData.testType) {
      this[metricType].testType = additionalData.testType;
    }
  }
  
  return this.save();
};

healthMetricsSchema.methods.getFormattedMetrics = function() {
  return {
    heartRate: this.heartRate?.current || null,
    bloodPressure: this.bloodPressure?.systolic && this.bloodPressure?.diastolic 
      ? `${this.bloodPressure.systolic}/${this.bloodPressure.diastolic}` 
      : null,
    weight: this.weight?.current || null,
    bloodSugar: this.bloodSugar?.current || null,
    height: this.height?.current || null,
    bmi: this.bmi || null,
    healthStatus: this.healthStatus,
    lastUpdated: this.updatedAt
  };
};

// Static methods
healthMetricsSchema.statics.findByPatientId = function(patientId) {
  return this.findOne({ patientId });
};

healthMetricsSchema.statics.createOrUpdateMetrics = async function(patientId, metricsData) {
  let healthMetrics = await this.findOne({ patientId });
  
  if (!healthMetrics) {
    healthMetrics = new this({ patientId, ...metricsData });
  } else {
    // Update existing metrics
    Object.keys(metricsData).forEach(key => {
      if (healthMetrics[key] && typeof metricsData[key] === 'object') {
        Object.assign(healthMetrics[key], metricsData[key]);
        healthMetrics[key].lastUpdated = new Date();
      }
    });
  }
  
  return healthMetrics.save();
};

// Indexes for better performance
healthMetricsSchema.index({ patientId: 1 });
healthMetricsSchema.index({ updatedAt: -1 });

const HealthMetrics = mongoose.model('HealthMetrics', healthMetricsSchema);
module.exports = HealthMetrics;
