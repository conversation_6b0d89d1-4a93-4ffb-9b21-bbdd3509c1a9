const express = require('express');
const cors = require('cors');
const http = require('http');
const socketIo = require('socket.io');
const connectDB = require('./db/connect');
const userRoutes = require('./routes/userRoutes');
const alertRoutes = require('./routes/alertRoutes');
const doctorDashboardRoutes = require('./routes/doctor/dashboardRoutes');
const doctorProfileRoutes = require('./routes/doctor/profileRoutes');
const patientRoutes = require('./routes/doctor/patientRoutes');
const doctorReportRoutes = require('./routes/doctor/reportRoutes');
const patientHealthRoutes = require('./routes/patient/healthRoutes');
const patientDashboardRoutes = require('./routes/patient/dashboardRoutes');
const path = require('path');
require('dotenv').config();

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: process.env.FRONTEND_URL || "http://localhost:3000",
    methods: ["GET", "POST", "PUT", "DELETE"]
  }
});
const port = process.env.PORT || 5000;

if (!process.env.MONGO_URI) {
  console.error('Error: MONGO_URI is not defined in .env file');
  process.exit(1);
}

app.use(cors());
app.use(express.json());
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// Make io available to routes
app.set('io', io);

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log('User connected:', socket.id);

  // Join patient-specific room for real-time updates
  socket.on('joinPatientRoom', (patientId) => {
    socket.join(`patient_${patientId}`);
    console.log(`Patient ${patientId} joined room`);
  });

  // Leave patient room
  socket.on('leavePatientRoom', (patientId) => {
    socket.leave(`patient_${patientId}`);
    console.log(`Patient ${patientId} left room`);
  });

  socket.on('disconnect', () => {
    console.log('User disconnected:', socket.id);
  });
});

const startServer = async () => {
  try {
    await connectDB();
    console.log('MongoDB connected successfully');

    app.use('/api', userRoutes);
    app.use('/api/alerts', alertRoutes);

    app.use('/api/doctor', (req, res, next) => {
      console.log(`>>> Doctor Path Request: ${req.method} ${req.originalUrl}`);
      next();
    });

    app.use('/api/doctor/dashboard', doctorDashboardRoutes);
    app.use('/api/doctor/profile', doctorProfileRoutes);
    app.use('/api/doctor/patient', patientRoutes);
    app.use('/api/doctor/reports', doctorReportRoutes);

    // Patient routes
    app.use('/api/patient', (req, res, next) => {
      console.log(`>>> Patient Path Request: ${req.method} ${req.originalUrl}`);
      next();
    });

    app.use('/api/patient/health', patientHealthRoutes);
    app.use('/api/patient/dashboard', patientDashboardRoutes);

    app.use((err, req, res, next) => {
      console.error("Unhandled Error:", err.stack || err);
      const statusCode = err.statusCode || 500;
      const message = process.env.NODE_ENV === 'production' ? 'Internal Server Error' : err.message || 'Something went wrong!';
      if (!res.headersSent) {
        res.status(statusCode).json({ message });
      } else {
        next(err);
      }
    });

    server.listen(port, () => {
      console.log(`Server running at http://localhost:${port}`);
      console.log('Socket.IO server initialized');
    });
  } catch (error) {
    console.error('Failed to connect to MongoDB:', error.message);
    process.exit(1);
  }
};

startServer();