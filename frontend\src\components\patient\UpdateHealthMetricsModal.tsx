import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useAuth } from '@/context/AuthContext';
import { Loader2 } from 'lucide-react';

interface UpdateHealthMetricsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUpdate: () => void;
  currentMetrics?: any;
}

const UpdateHealthMetricsModal: React.FC<UpdateHealthMetricsModalProps> = ({
  isOpen,
  onClose,
  onUpdate,
  currentMetrics
}) => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    heartRate: currentMetrics?.heartRate || '',
    systolic: currentMetrics?.bloodPressure?.split('/')[0] || '',
    diastolic: currentMetrics?.bloodPressure?.split('/')[1] || '',
    weight: currentMetrics?.weight || '',
    bloodSugar: currentMetrics?.bloodSugar || '',
    bloodSugarType: 'random',
    height: currentMetrics?.height || ''
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const updateData: any = {};

      // Only include fields that have values
      if (formData.heartRate) {
        updateData.heartRate = parseInt(formData.heartRate);
      }

      if (formData.systolic && formData.diastolic) {
        updateData.bloodPressure = {
          systolic: parseInt(formData.systolic),
          diastolic: parseInt(formData.diastolic)
        };
      }

      if (formData.weight) {
        updateData.weight = parseFloat(formData.weight);
      }

      if (formData.bloodSugar) {
        updateData.bloodSugar = {
          value: parseInt(formData.bloodSugar),
          testType: formData.bloodSugarType
        };
      }

      if (formData.height) {
        updateData.height = parseInt(formData.height);
      }

      const response = await fetch('/api/patient/health/metrics', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(updateData)
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || 'Failed to update health metrics');
      }

      // Success
      onUpdate();
      onClose();
      
      // Reset form
      setFormData({
        heartRate: '',
        systolic: '',
        diastolic: '',
        weight: '',
        bloodSugar: '',
        bloodSugarType: 'random',
        height: ''
      });

    } catch (error) {
      console.error('Error updating health metrics:', error);
      alert('Failed to update health metrics. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Update Health Metrics</DialogTitle>
          <DialogDescription>
            Update your current health measurements. Leave fields empty if you don't want to update them.
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="heartRate">Heart Rate (bpm)</Label>
              <Input
                id="heartRate"
                type="number"
                min="30"
                max="220"
                value={formData.heartRate}
                onChange={(e) => handleInputChange('heartRate', e.target.value)}
                placeholder="72"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="weight">Weight (kg)</Label>
              <Input
                id="weight"
                type="number"
                min="20"
                max="500"
                step="0.1"
                value={formData.weight}
                onChange={(e) => handleInputChange('weight', e.target.value)}
                placeholder="70.5"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label>Blood Pressure (mmHg)</Label>
            <div className="grid grid-cols-2 gap-2">
              <Input
                type="number"
                min="70"
                max="250"
                value={formData.systolic}
                onChange={(e) => handleInputChange('systolic', e.target.value)}
                placeholder="120 (Systolic)"
              />
              <Input
                type="number"
                min="40"
                max="150"
                value={formData.diastolic}
                onChange={(e) => handleInputChange('diastolic', e.target.value)}
                placeholder="80 (Diastolic)"
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="bloodSugar">Blood Sugar (mg/dL)</Label>
              <Input
                id="bloodSugar"
                type="number"
                min="50"
                max="600"
                value={formData.bloodSugar}
                onChange={(e) => handleInputChange('bloodSugar', e.target.value)}
                placeholder="90"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="bloodSugarType">Test Type</Label>
              <Select value={formData.bloodSugarType} onValueChange={(value) => handleInputChange('bloodSugarType', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="fasting">Fasting</SelectItem>
                  <SelectItem value="random">Random</SelectItem>
                  <SelectItem value="postprandial">Post-meal</SelectItem>
                  <SelectItem value="hba1c">HbA1c</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="height">Height (cm)</Label>
            <Input
              id="height"
              type="number"
              min="50"
              max="250"
              value={formData.height}
              onChange={(e) => handleInputChange('height', e.target.value)}
              placeholder="175"
            />
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Update Metrics
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default UpdateHealthMetricsModal;
