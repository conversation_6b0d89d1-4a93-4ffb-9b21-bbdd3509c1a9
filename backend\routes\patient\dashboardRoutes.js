const express = require('express');
const router = express.Router();
const { protect, requirePatientRole } = require('../../middleware/authMiddleware');
const {
  getDashboardData,
  getHealthOverview,
  getHealthStats
} = require('../../controllers/patient/dashboardController');

// Apply authentication middleware to all routes
router.use(protect);
router.use(requirePatientRole);

// Dashboard routes
router.get('/', getDashboardData);
router.get('/overview', getHealthOverview);
router.get('/stats', getHealthStats);

module.exports = router;
