const mongoose = require('mongoose');

const healthReadingSchema = new mongoose.Schema({
  patientId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  metricType: {
    type: String,
    required: true,
    enum: ['heartRate', 'bloodPressure', 'weight', 'bloodSugar', 'height'],
    index: true
  },
  value: {
    type: mongoose.Schema.Types.Mixed,
    required: true
  },
  unit: {
    type: String,
    required: true
  },
  // Additional data for specific metrics
  additionalData: {
    // For blood pressure
    systolic: Number,
    diastolic: Number,
    
    // For blood sugar
    testType: {
      type: String,
      enum: ['fasting', 'random', 'postprandial', 'hba1c']
    },
    
    // For general readings
    notes: String,
    deviceId: String,
    deviceType: String
  },
  // Reading metadata
  readingTime: {
    type: Date,
    required: true,
    default: Date.now,
    index: true
  },
  source: {
    type: String,
    enum: ['manual', 'device', 'doctor', 'nurse', 'app'],
    default: 'manual'
  },
  // Flags
  isAlert: {
    type: Boolean,
    default: false
  },
  alertLevel: {
    type: String,
    enum: ['normal', 'warning', 'critical'],
    default: 'normal'
  },
  // Timestamps
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// Pre-save middleware
healthReadingSchema.pre('save', function(next) {
  // Auto-detect alerts based on values
  this.detectAlerts();
  next();
});

// Instance methods
healthReadingSchema.methods.detectAlerts = function() {
  const { metricType, value, additionalData } = this;
  
  switch (metricType) {
    case 'heartRate':
      if (value < 60 || value > 100) {
        this.isAlert = true;
        this.alertLevel = value < 50 || value > 120 ? 'critical' : 'warning';
      }
      break;
      
    case 'bloodPressure':
      if (additionalData?.systolic && additionalData?.diastolic) {
        const { systolic, diastolic } = additionalData;
        if (systolic > 140 || diastolic > 90 || systolic < 90 || diastolic < 60) {
          this.isAlert = true;
          this.alertLevel = systolic > 180 || diastolic > 110 || systolic < 80 ? 'critical' : 'warning';
        }
      }
      break;
      
    case 'bloodSugar':
      const testType = additionalData?.testType || 'random';
      if (testType === 'fasting' && (value > 126 || value < 70)) {
        this.isAlert = true;
        this.alertLevel = value > 200 || value < 50 ? 'critical' : 'warning';
      } else if (testType === 'random' && (value > 200 || value < 70)) {
        this.isAlert = true;
        this.alertLevel = value > 300 || value < 50 ? 'critical' : 'warning';
      }
      break;
  }
};

// Static methods
healthReadingSchema.statics.getReadingsByPatient = function(patientId, options = {}) {
  const {
    metricType,
    startDate,
    endDate,
    limit = 100,
    sort = { readingTime: -1 }
  } = options;
  
  let query = { patientId };
  
  if (metricType) {
    query.metricType = metricType;
  }
  
  if (startDate || endDate) {
    query.readingTime = {};
    if (startDate) query.readingTime.$gte = new Date(startDate);
    if (endDate) query.readingTime.$lte = new Date(endDate);
  }
  
  return this.find(query)
    .sort(sort)
    .limit(limit);
};

healthReadingSchema.statics.getLatestReading = function(patientId, metricType) {
  return this.findOne({ patientId, metricType })
    .sort({ readingTime: -1 });
};

healthReadingSchema.statics.getReadingsForChart = function(patientId, metricType, days = 7) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  return this.find({
    patientId,
    metricType,
    readingTime: { $gte: startDate }
  })
  .sort({ readingTime: 1 })
  .select('value additionalData readingTime unit');
};

healthReadingSchema.statics.getWeeklyChartData = async function(patientId, metricType) {
  const endDate = new Date();
  const startDate = new Date();
  startDate.setDate(endDate.getDate() - 6); // Last 7 days
  
  // Get all readings for the week
  const readings = await this.find({
    patientId,
    metricType,
    readingTime: { 
      $gte: startDate,
      $lte: endDate
    }
  }).sort({ readingTime: 1 });
  
  // Create array for each day of the week
  const weekData = [];
  const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
  
  for (let i = 0; i < 7; i++) {
    const currentDate = new Date(startDate);
    currentDate.setDate(startDate.getDate() + i);
    
    const dayReadings = readings.filter(reading => {
      const readingDate = new Date(reading.readingTime);
      return readingDate.toDateString() === currentDate.toDateString();
    });
    
    let value = null;
    if (dayReadings.length > 0) {
      // Use the latest reading of the day or average if multiple
      if (metricType === 'bloodPressure') {
        const latestReading = dayReadings[dayReadings.length - 1];
        value = latestReading.additionalData?.systolic || null;
      } else {
        // For other metrics, use average if multiple readings
        const sum = dayReadings.reduce((acc, reading) => acc + reading.value, 0);
        value = Math.round(sum / dayReadings.length);
      }
    }
    
    weekData.push({
      name: dayNames[currentDate.getDay()],
      value: value,
      date: currentDate.toISOString().split('T')[0]
    });
  }
  
  return weekData;
};

// Indexes for better performance
healthReadingSchema.index({ patientId: 1, metricType: 1, readingTime: -1 });
healthReadingSchema.index({ patientId: 1, readingTime: -1 });
healthReadingSchema.index({ readingTime: -1 });

const HealthReading = mongoose.model('HealthReading', healthReadingSchema);
module.exports = HealthReading;
